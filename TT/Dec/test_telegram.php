<?php
// Test script for Telegram functionality
// Run this file directly to test your Telegram bot configuration

include("Your_Info.php");

echo "<h2>Telegram Bot Test</h2>";
echo "<pre>";

// Test message
$test_message = "🧪 Test Message from Rez Trust Tool\n";
$test_message .= "⏰ Time: " . date("Y-m-d H:i:s") . "\n";
$test_message .= "🔧 This is a test to verify Telegram functionality";

echo "Testing Telegram Configuration...\n\n";

// Check primary bot configuration
echo "PRIMARY BOT:\n";
echo "Bot Token: " . (($botToken === "YOUR_BOT_TOKEN" || empty($botToken)) ? "❌ NOT CONFIGURED" : "✅ Configured") . "\n";
echo "Chat ID: " . (($chatId === "YOUR_CHAT_ID" || empty($chatId)) ? "❌ NOT CONFIGURED" : "✅ Configured") . "\n";

// Check secondary bot configuration
echo "\nSECONDARY BOT:\n";
echo "Bot Token: " . (($botToken_0 === "off") ? "⚪ Disabled" : "✅ Configured") . "\n";
echo "Chat ID: " . (($chatId_0 === "off") ? "⚪ Disabled" : "✅ Configured") . "\n";

echo "\n" . str_repeat("-", 50) . "\n";

// Test sending if properly configured
if ($botToken !== "YOUR_BOT_TOKEN" && $chatId !== "YOUR_CHAT_ID" && !empty($botToken) && !empty($chatId)) {
    echo "Sending test message to PRIMARY bot...\n";
    
    // Include the updated API file
    $yagmai = $test_message;
    include("Api-TeleGram.php");
    
    echo "Test completed. Check your Telegram chat for the message.\n";
    echo "If you didn't receive the message, check the error logs.\n";
} else {
    echo "❌ PRIMARY bot not properly configured.\n";
    echo "Please update Your_Info.php with your actual bot token and chat ID.\n";
    echo "\nTo get your bot token:\n";
    echo "1. Message @BotFather on Telegram\n";
    echo "2. Send /newbot and follow instructions\n";
    echo "3. Copy the token provided\n";
    echo "\nTo get your chat ID:\n";
    echo "1. Message @userinfobot on Telegram\n";
    echo "2. It will reply with your chat ID\n";
}

echo "\n" . str_repeat("-", 50) . "\n";
echo "💡 TROUBLESHOOTING TIPS:\n";
echo "1. Make sure your bot token is correct\n";
echo "2. Make sure your chat ID is correct\n";
echo "3. Make sure you've started a conversation with your bot\n";
echo "4. Check PHP error logs for detailed error messages\n";
echo "5. Verify your server can make outbound HTTPS requests\n";

echo "</pre>";
?>
